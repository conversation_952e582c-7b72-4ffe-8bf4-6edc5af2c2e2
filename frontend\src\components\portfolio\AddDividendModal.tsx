"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useState } from "react";

interface TradingCode {
  id: number;
  code: string;
  name: string;
}

interface AddDividendModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDividendAdded: () => void;
}

interface DividendFormData {
  trading_code_id: string;
  record_date: string;
  dividend_per_share: string;
  share_count: string;
}

export default function AddDividendModal({
  isOpen,
  onClose,
  onDividendAdded,
}: AddDividendModalProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [tradingCodes, setTradingCodes] = useState<TradingCode[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<DividendFormData>({
    trading_code_id: "",
    record_date: "",
    dividend_per_share: "",
    share_count: "",
  });

  const fetchTradingCodes = useCallback(async () => {
    if (!session?.user?.accessToken) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/trading-codes`,
        {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setTradingCodes(data);
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch trading codes.",
        });
      }
    } catch (error) {
      console.error("Error fetching trading codes:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while fetching trading codes.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [session, toast]);

  useEffect(() => {
    if (isOpen) {
      fetchTradingCodes();
    }
  }, [isOpen, fetchTradingCodes]);

  const handleInputChange = (field: keyof DividendFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session?.user?.accessToken) return;

    // Validate form
    if (!formData.trading_code_id || !formData.record_date || 
        !formData.dividend_per_share || !formData.share_count) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill in all required fields.",
      });
      return;
    }

    const dividendPerShare = parseFloat(formData.dividend_per_share);
    const shareCount = parseInt(formData.share_count);

    if (isNaN(dividendPerShare) || dividendPerShare <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Dividend per share must be a positive number.",
      });
      return;
    }

    if (isNaN(shareCount) || shareCount <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Share count must be a positive integer.",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/dividend-entries`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.user.accessToken}`,
          },
          body: JSON.stringify({
            trading_code_id: parseInt(formData.trading_code_id),
            record_date: formData.record_date,
            dividend_per_share: dividendPerShare,
            share_count: shareCount,
          }),
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Dividend entry added successfully.",
          className: "bg-green-50 border-green-200 text-green-800",
        });
        
        // Reset form
        setFormData({
          trading_code_id: "",
          record_date: "",
          dividend_per_share: "",
          share_count: "",
        });
        
        onDividendAdded();
        onClose();
      } else {
        const errorData = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: errorData.detail || "Failed to add dividend entry.",
        });
      }
    } catch (error) {
      console.error("Error adding dividend entry:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while adding the dividend entry.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      trading_code_id: "",
      record_date: "",
      dividend_per_share: "",
      share_count: "",
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Add Dividend Entry
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="trading_code">Trading Code</Label>
            <Select
              value={formData.trading_code_id}
              onValueChange={(value) => handleInputChange("trading_code_id", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select trading code" />
              </SelectTrigger>
              <SelectContent>
                {isLoading ? (
                  <SelectItem value="" disabled>
                    Loading...
                  </SelectItem>
                ) : (
                  tradingCodes.map((code) => (
                    <SelectItem key={code.id} value={code.id.toString()}>
                      {code.code} - {code.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="record_date">Record Date</Label>
            <Input
              id="record_date"
              type="date"
              value={formData.record_date}
              onChange={(e) => handleInputChange("record_date", e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dividend_per_share">Dividend per Share (BDT)</Label>
            <Input
              id="dividend_per_share"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.dividend_per_share}
              onChange={(e) => handleInputChange("dividend_per_share", e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="share_count">Share Count</Label>
            <Input
              id="share_count"
              type="number"
              min="1"
              placeholder="0"
              value={formData.share_count}
              onChange={(e) => handleInputChange("share_count", e.target.value)}
              required
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Dividend"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
