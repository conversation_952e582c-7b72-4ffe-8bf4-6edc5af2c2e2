#!/usr/bin/env python3
"""
Simple migration runner for adding dividend_received_date column
"""
import sqlite3
import os
import sys

def run_migration():
    """Run the migration to add dividend_received_date column"""
    
    # Check if we're using SQLite (for development)
    db_path = os.path.join(os.path.dirname(__file__), '..', 'backend', 'app.db')
    
    if os.path.exists(db_path):
        print(f"Found SQLite database at: {db_path}")
        
        # SQLite migration
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # Check if column already exists
            cursor.execute("PRAGMA table_info(received_dividends)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'dividend_received_date' not in columns:
                print("Adding dividend_received_date column...")
                cursor.execute("""
                    ALTER TABLE received_dividends 
                    ADD COLUMN dividend_received_date DATE NULL
                """)
                
                # Create index
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_received_dividends_received_date 
                    ON received_dividends(dividend_received_date)
                """)
                
                conn.commit()
                print("Migration completed successfully!")
            else:
                print("Column dividend_received_date already exists.")
                
        except Exception as e:
            print(f"Migration failed: {e}")
            conn.rollback()
        finally:
            conn.close()
    else:
        print("SQLite database not found. Please run this migration manually on your PostgreSQL database:")
        print()
        with open(os.path.join(os.path.dirname(__file__), 'migrations', 'add_dividend_received_date.sql'), 'r') as f:
            print(f.read())

if __name__ == "__main__":
    run_migration()
