"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { ArrowUpDown } from "lucide-react";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useMemo, useState } from "react";
import EditDividendModal from "./EditDividendModal";

interface ReceivedDividend {
  id: number;
  portfolio_id: number;
  trading_code_id: number;
  record_date: string;
  share_count: number;
  dividend_per_share: number;
  gross_dividend: number | string | null;
  tax_amount: number | string | null;
  net_dividend: number | string | null;
  dividend_received_date: string | null;
  created_at: string;
  trading_code: string;
  trading_name: string;
}

interface DividendDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-BD", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return "N/A";
  }
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Helper to safely parse numbers
const safeParseFloat = (value: unknown): number => {
  const parsed = parseFloat(String(value));
  return isNaN(parsed) ? 0 : parsed;
};

export default function DividendDetailsModal({
  isOpen,
  onClose,
}: DividendDetailsModalProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [dividends, setDividends] = useState<ReceivedDividend[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [sortField, setSortField] = useState<
    "trading_code" | "record_date" | "dividend_received_date"
  >("record_date");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedDividend, setSelectedDividend] =
    useState<ReceivedDividend | null>(null);

  const fetchDividends = useCallback(async () => {
    if (!session?.user?.accessToken) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/received-dividends`,
        {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setDividends(data);
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch dividend data.",
        });
      }
    } catch (error) {
      console.error("Error fetching dividends:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while fetching dividend data.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [session, toast]);

  useEffect(() => {
    if (isOpen) {
      fetchDividends();
    }
  }, [isOpen, fetchDividends]);

  const filteredAndSortedDividends = useMemo(() => {
    let filtered = dividends;

    // Filter by received date if dates are provided
    if (startDate || endDate) {
      filtered = dividends.filter((dividend) => {
        const receivedDate = dividend.dividend_received_date
          ? new Date(dividend.dividend_received_date)
          : null;
        const start = startDate ? new Date(startDate) : null;
        const end = endDate ? new Date(endDate) : null;

        // If no received date, don't include in filtered results when date filter is applied
        if (!receivedDate) return false;

        if (start && receivedDate < start) return false;
        if (end && receivedDate > end) return false;
        return true;
      });
    }

    // Sort the filtered results
    return [...filtered].sort((a, b) => {
      let aValue: string | Date;
      let bValue: string | Date;

      switch (sortField) {
        case "trading_code":
          aValue = a.trading_code;
          bValue = b.trading_code;
          break;
        case "record_date":
          aValue = new Date(a.record_date);
          bValue = new Date(b.record_date);
          break;
        case "dividend_received_date":
          aValue = a.dividend_received_date
            ? new Date(a.dividend_received_date)
            : new Date(0);
          bValue = b.dividend_received_date
            ? new Date(b.dividend_received_date)
            : new Date(0);
          break;
        default:
          aValue = new Date(a.record_date);
          bValue = new Date(b.record_date);
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        return sortDirection === "asc"
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      } else {
        const aStr = String(aValue);
        const bStr = String(bValue);
        return sortDirection === "asc"
          ? aStr.localeCompare(bStr)
          : bStr.localeCompare(aStr);
      }
    });
  }, [dividends, startDate, endDate, sortField, sortDirection]);

  const summary = useMemo(() => {
    return filteredAndSortedDividends.reduce(
      (acc, dividend) => ({
        totalDividendReceived:
          acc.totalDividendReceived + safeParseFloat(dividend.gross_dividend),
        totalTaxDeducted:
          acc.totalTaxDeducted + safeParseFloat(dividend.tax_amount),
        totalNetDividend:
          acc.totalNetDividend + safeParseFloat(dividend.net_dividend),
      }),
      {
        totalDividendReceived: 0,
        totalTaxDeducted: 0,
        totalNetDividend: 0,
      }
    );
  }, [filteredAndSortedDividends]);

  const clearFilters = () => {
    setStartDate("");
    setEndDate("");
  };

  const handleSort = (
    field: "trading_code" | "record_date" | "dividend_received_date"
  ) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleRowDoubleClick = (dividend: ReceivedDividend) => {
    setSelectedDividend(dividend);
    setIsEditModalOpen(true);
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setSelectedDividend(null);
  };

  const handleDividendUpdated = () => {
    fetchDividends();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="!w-full !max-w-[950px] max-h-[95vh] flex flex-col p-0">
        <DialogHeader className="px-6 pt-6">
          <DialogTitle className="text-xl font-bold">
            Received Dividends Details
          </DialogTitle>
        </DialogHeader>

        {/* Date Range Filter - Filters by Received Date */}
        <div className="flex items-center gap-4 px-6 pt-1 pb-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="startDate">Received From:</Label>
            <Input
              id="startDate"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-40"
            />
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="endDate">Received To:</Label>
            <Input
              id="endDate"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-40"
            />
          </div>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>

        {/* Scrollable Dividends Table Container */}
        <div className="relative flex-1 border rounded-lg overflow-y-auto mx-6 mb-6">
          {isLoading ? (
            <div className="p-8 text-center">Loading dividend data...</div>
          ) : filteredAndSortedDividends.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              No dividend data found for the selected period.
            </div>
          ) : (
            <Table>
              <TableHeader className="sticky top-0 z-10 bg-background">
                <TableRow>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("trading_code")}
                      className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                      Trading Code
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("record_date")}
                      className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                      Record Date
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                  </TableHead>
                  <TableHead>Dividend/Share</TableHead>
                  <TableHead>Share Count</TableHead>
                  <TableHead>Gross Dividend</TableHead>
                  <TableHead>Tax Deducted</TableHead>
                  <TableHead>Net Dividend</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("dividend_received_date")}
                      className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                      Received Date
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedDividends.map((dividend) => (
                  <TableRow
                    key={dividend.id}
                    onDoubleClick={() => handleRowDoubleClick(dividend)}
                    className="cursor-pointer hover:bg-muted/50"
                  >
                    <TableCell className="font-medium">
                      {dividend.trading_code}
                    </TableCell>
                    <TableCell>{formatDate(dividend.record_date)}</TableCell>
                    <TableCell>
                      {formatCurrency(
                        safeParseFloat(dividend.dividend_per_share)
                      )}
                    </TableCell>
                    <TableCell>
                      {new Intl.NumberFormat().format(
                        safeParseFloat(dividend.share_count)
                      )}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(safeParseFloat(dividend.gross_dividend))}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(safeParseFloat(dividend.tax_amount))}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(safeParseFloat(dividend.net_dividend))}
                    </TableCell>
                    <TableCell>
                      {dividend.dividend_received_date
                        ? formatDate(dividend.dividend_received_date)
                        : "—"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter className="sticky bottom-0 z-10 bg-background border-t-2">
                <TableRow className="font-bold">
                  <TableCell colSpan={4} className="text-right">
                    Total:
                  </TableCell>
                  <TableCell>
                    {formatCurrency(summary.totalDividendReceived)}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(summary.totalTaxDeducted)}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(summary.totalNetDividend)}
                  </TableCell>
                  <TableCell>—</TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          )}
        </div>
      </DialogContent>

      <EditDividendModal
        isOpen={isEditModalOpen}
        onClose={handleEditModalClose}
        onDividendUpdated={handleDividendUpdated}
        dividend={selectedDividend}
      />
    </Dialog>
  );
}
