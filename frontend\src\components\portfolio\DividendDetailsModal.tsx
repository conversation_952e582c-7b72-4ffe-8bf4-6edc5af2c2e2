"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useMemo, useState } from "react";

interface ReceivedDividend {
  id: number;
  portfolio_id: number;
  trading_code_id: number;
  record_date: string;
  share_count: number;
  dividend_per_share: number;
  gross_dividend: number | string | null;
  tax_amount: number | string | null;
  net_dividend: number | string | null;
  dividend_received_date: string | null;
  created_at: string;
  trading_code: string;
  trading_name: string;
}

interface DividendDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-BD", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return "N/A";
  }
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Helper to safely parse numbers
const safeParseFloat = (value: unknown): number => {
  const parsed = parseFloat(String(value));
  return isNaN(parsed) ? 0 : parsed;
};

export default function DividendDetailsModal({
  isOpen,
  onClose,
}: DividendDetailsModalProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [dividends, setDividends] = useState<ReceivedDividend[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  const fetchDividends = useCallback(async () => {
    if (!session?.user?.accessToken) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/received-dividends`,
        {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setDividends(data);
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch dividend data.",
        });
      }
    } catch (error) {
      console.error("Error fetching dividends:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while fetching dividend data.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [session, toast]);

  useEffect(() => {
    if (isOpen) {
      fetchDividends();
    }
  }, [isOpen, fetchDividends]);

  const filteredDividends = useMemo(() => {
    if (!startDate && !endDate) return dividends;

    return dividends.filter((dividend) => {
      const recordDate = new Date(dividend.record_date);
      const start = startDate ? new Date(startDate) : null;
      const end = endDate ? new Date(endDate) : null;

      if (start && recordDate < start) return false;
      if (end && recordDate > end) return false;
      return true;
    });
  }, [dividends, startDate, endDate]);

  const summary = useMemo(() => {
    return filteredDividends.reduce(
      (acc, dividend) => ({
        totalDividendReceived:
          acc.totalDividendReceived + safeParseFloat(dividend.gross_dividend),
        totalTaxDeducted:
          acc.totalTaxDeducted + safeParseFloat(dividend.tax_amount),
        totalNetDividend:
          acc.totalNetDividend + safeParseFloat(dividend.net_dividend),
      }),
      {
        totalDividendReceived: 0,
        totalTaxDeducted: 0,
        totalNetDividend: 0,
      }
    );
  }, [filteredDividends]);

  const clearFilters = () => {
    setStartDate("");
    setEndDate("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="!w-full !max-w-[900px] max-h-[95vh] flex flex-col p-0">
        <DialogHeader className="px-6 pt-6">
          <DialogTitle className="text-xl font-bold">
            Received Dividends Details
          </DialogTitle>
        </DialogHeader>

        {/* Date Range Filter */}
        <div className="flex items-center gap-4 px-6 pt-1 pb-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="startDate">From:</Label>
            <Input
              id="startDate"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-40"
            />
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="endDate">To:</Label>
            <Input
              id="endDate"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-40"
            />
          </div>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>

        {/* Scrollable Dividends Table Container */}
        <div className="relative flex-1 border rounded-lg overflow-y-auto mx-6 mb-6">
          {isLoading ? (
            <div className="p-8 text-center">Loading dividend data...</div>
          ) : filteredDividends.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              No dividend data found for the selected period.
            </div>
          ) : (
            <Table>
              <TableHeader className="sticky top-0 z-10 bg-background">
                <TableRow>
                  <TableHead>Trading Code</TableHead>
                  <TableHead>Record Date</TableHead>
                  <TableHead>Dividend/Share</TableHead>
                  <TableHead>Share Count</TableHead>
                  <TableHead>Gross Dividend</TableHead>
                  <TableHead>Tax Deducted</TableHead>
                  <TableHead>Net Dividend</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDividends.map((dividend) => (
                  <TableRow key={dividend.id}>
                    <TableCell className="font-medium">
                      {dividend.trading_code}
                    </TableCell>
                    <TableCell>{formatDate(dividend.record_date)}</TableCell>
                    <TableCell>
                      {formatCurrency(
                        safeParseFloat(dividend.dividend_per_share)
                      )}
                    </TableCell>
                    <TableCell>
                      {new Intl.NumberFormat().format(
                        safeParseFloat(dividend.share_count)
                      )}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(safeParseFloat(dividend.gross_dividend))}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(safeParseFloat(dividend.tax_amount))}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(safeParseFloat(dividend.net_dividend))}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter className="sticky bottom-0 z-10 bg-background border-t-2">
                <TableRow className="font-bold">
                  <TableCell colSpan={4} className="text-right">
                    Total:
                  </TableCell>
                  <TableCell>
                    {formatCurrency(summary.totalDividendReceived)}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(summary.totalTaxDeducted)}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(summary.totalNetDividend)}
                  </TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
