from datetime import date, datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel


class DividendInfoBase(BaseModel):
    last_record_date: Optional[date] = None
    next_record_date: Optional[date] = None
    dividend_per_share: Decimal = Decimal("0.00")


class DividendInfoCreate(DividendInfoBase):
    portfolio_id: int
    trading_code_id: int


class DividendInfoUpdate(BaseModel):
    last_record_date: Optional[date] = None
    next_record_date: Optional[date] = None
    dividend_per_share: Optional[Decimal] = None


class DividendInfoInDBBase(DividendInfoBase):
    id: int
    portfolio_id: int
    trading_code_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DividendInfo(DividendInfoInDBBase):
    pass


class DividendInfoResponse(DividendInfoInDBBase):
    pass


class ReceivedDividendBase(BaseModel):
    record_date: date
    share_count: int
    dividend_per_share: Decimal
    gross_dividend: Decimal
    tax_amount: Decimal
    net_dividend: Decimal


class ReceivedDividendCreate(ReceivedDividendBase):
    portfolio_id: int
    trading_code_id: int


class ReceivedDividendInDBBase(ReceivedDividendBase):
    id: int
    portfolio_id: int
    trading_code_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class ReceivedDividend(ReceivedDividendInDBBase):
    pass


class ReceivedDividendResponse(ReceivedDividendInDBBase):
    pass


class DividendInfoWithTradingCode(DividendInfo):
    trading_code: str
    trading_name: str


class ReceivedDividendWithTradingCode(ReceivedDividend):
    trading_code: str
    trading_name: str


class ReceivedDividendSummary(BaseModel):
    total_dividend_received: Decimal
    total_tax_deducted: Decimal
    total_net_dividend: Decimal
