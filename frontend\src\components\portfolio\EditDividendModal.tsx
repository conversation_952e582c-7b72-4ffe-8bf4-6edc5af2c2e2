"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

interface EditDividendModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDividendUpdated: () => void;
  dividend: {
    id: number;
    trading_code: string;
    trading_name: string;
    record_date: string;
    dividend_per_share: number;
    share_count: number;
    dividend_received_date?: string | null;
  } | null;
}

interface EditFormData {
  dividend_per_share: string;
  share_count: string;
  dividend_received_date: string;
}

export default function EditDividendModal({
  isOpen,
  onClose,
  onDividendUpdated,
  dividend,
}: EditDividendModalProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<EditFormData>({
    dividend_per_share: "",
    share_count: "",
    dividend_received_date: "",
  });

  // Update form data when dividend prop changes
  useEffect(() => {
    if (dividend) {
      setFormData({
        dividend_per_share: dividend.dividend_per_share.toString(),
        share_count: dividend.share_count.toString(),
        dividend_received_date: dividend.dividend_received_date || "",
      });
    }
  }, [dividend]);

  const handleInputChange = (field: keyof EditFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session?.user?.accessToken || !dividend) return;

    // Validate form
    const dividendPerShare = parseFloat(formData.dividend_per_share);
    const shareCount = parseInt(formData.share_count);

    if (isNaN(dividendPerShare) || dividendPerShare <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Dividend per share must be a positive number.",
      });
      return;
    }

    if (isNaN(shareCount) || shareCount <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Share count must be a positive integer.",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const updateData: any = {
        dividend_per_share: dividendPerShare,
        share_count: shareCount,
      };

      // Only include dividend_received_date if it's provided
      if (formData.dividend_received_date) {
        updateData.dividend_received_date = formData.dividend_received_date;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/received-dividends/${dividend.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.user.accessToken}`,
          },
          body: JSON.stringify(updateData),
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Dividend entry updated successfully.",
          className: "bg-green-50 border-green-200 text-green-800",
        });
        
        onDividendUpdated();
        onClose();
      } else {
        const errorData = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: errorData.detail || "Failed to update dividend entry.",
        });
      }
    } catch (error) {
      console.error("Error updating dividend entry:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while updating the dividend entry.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      dividend_per_share: "",
      share_count: "",
      dividend_received_date: "",
    });
    onClose();
  };

  if (!dividend) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Edit Dividend Entry
          </DialogTitle>
          <p className="text-sm text-gray-600">
            {dividend.trading_code} - {dividend.trading_name}
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="record_date">Record Date (Read-only)</Label>
            <Input
              id="record_date"
              type="date"
              value={dividend.record_date}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dividend_per_share">Dividend per Share (BDT)</Label>
            <Input
              id="dividend_per_share"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.dividend_per_share}
              onChange={(e) => handleInputChange("dividend_per_share", e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="share_count">Share Count</Label>
            <Input
              id="share_count"
              type="number"
              min="1"
              placeholder="0"
              value={formData.share_count}
              onChange={(e) => handleInputChange("share_count", e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dividend_received_date">Dividend Received Date (Optional)</Label>
            <Input
              id="dividend_received_date"
              type="date"
              value={formData.dividend_received_date}
              onChange={(e) => handleInputChange("dividend_received_date", e.target.value)}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Dividend"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
