from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional

from app.api.deps import get_current_user, get_db
from app.core.security import verify_password
from app.crud import update
from app.models.dividend_info import DividendInfo
from app.models.portfolio import Portfolio
from app.models.portfolio_entry import PortfolioEntry
from app.models.received_dividend import ReceivedDividend
from app.models.trading_code import TradingCode
from app.models.user import User
from app.schemas.dividend import (DividendEntryCreate, DividendInfoResponse,
                                  DividendInfoUpdate, ReceivedDividendResponse,
                                  ReceivedDividendSummary,
                                  ReceivedDividendUpdate,
                                  ReceivedDividendWithTradingCode)
from app.schemas.portfolio import Portfolio as PortfolioSchema
from app.schemas.portfolio import (PortfolioEntryCreate,
                                   PortfolioEntryResponse, PortfolioFeesDeduct,
                                   PortfolioInvestmentAdd,
                                   PortfolioRealizedGainAdd,
                                   PortfolioRealizedLossAdd, PortfolioUpdate)
from app.schemas.user import UserResponse, UserUpdate
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def read_user_me(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user information
    """
    # Create access token for the response
    return {
        "id": current_user.id,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "is_admin": current_user.is_admin,
        "access_token": "",  # Token is already in the Authorization header
    }


@router.patch("/me", response_model=UserResponse)
async def update_user_me(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    user_in: UserUpdate,
) -> Any:
    """
    Update current user information
    """
    # If updating password, verify current password
    if user_in.password and user_in.current_password:
        if not verify_password(user_in.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Incorrect password",
            )

        # Remove current_password from the update data
        user_in_dict = user_in.dict(exclude_unset=True)
        if "current_password" in user_in_dict:
            del user_in_dict["current_password"]
        user_in = user_in_dict

    # Update user
    user = await update(db, db_obj=current_user, obj_in=user_in)

    # Return updated user info
    return {
        "id": user.id,
        "email": user.email,
        "full_name": user.full_name,
        "is_admin": user.is_admin,
        "access_token": "",  # Token is already in the Authorization header
    }


@router.get("/portfolio", response_model=PortfolioSchema)
async def get_user_portfolio(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's portfolio settings
    """
    # Check if user has a portfolio
    result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = result.scalars().first()

    # If no portfolio exists, create one with default values
    if not portfolio:
        portfolio = Portfolio(user_id=current_user.id, commission=0.0)
        db.add(portfolio)
        await db.commit()
        await db.refresh(portfolio)

    return portfolio


@router.patch("/portfolio", response_model=PortfolioSchema)
async def update_user_portfolio(
    portfolio_in: PortfolioUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update current user's portfolio settings
    """
    # Get user's portfolio
    result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = result.scalars().first()

    # If no portfolio exists, create one
    if not portfolio:
        portfolio = Portfolio(user_id=current_user.id)
        db.add(portfolio)

    # Update portfolio with new values
    if portfolio_in.commission is not None:
        portfolio.commission = portfolio_in.commission

    if portfolio_in.investment_amount is not None:
        portfolio.investment_amount = portfolio_in.investment_amount

    if portfolio_in.realized_gain is not None:
        portfolio.realized_gain = portfolio_in.realized_gain

    if portfolio_in.realized_loss is not None:
        portfolio.realized_loss = portfolio_in.realized_loss

    await db.commit()
    await db.refresh(portfolio)

    return portfolio


@router.post("/portfolio/add-investment", response_model=PortfolioSchema)
async def add_investment_amount(
    investment_in: PortfolioInvestmentAdd,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Add investment amount to current user's portfolio
    """
    # Get user's portfolio
    result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = result.scalars().first()

    # If no portfolio exists, create one
    if not portfolio:
        portfolio = Portfolio(
            user_id=current_user.id, investment_amount=investment_in.amount
        )
        db.add(portfolio)
    else:
        # Add to existing investment amount
        portfolio.investment_amount = (
            portfolio.investment_amount or 0.0
        ) + investment_in.amount

    await db.commit()
    await db.refresh(portfolio)

    return portfolio


@router.post("/portfolio/add-realized-gain", response_model=PortfolioSchema)
async def add_realized_gain(
    realized_gain_in: PortfolioRealizedGainAdd,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Add realized gain to current user's portfolio
    """
    # Get user's portfolio
    result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = result.scalars().first()

    # If no portfolio exists, create one
    if not portfolio:
        portfolio = Portfolio(
            user_id=current_user.id, realized_gain=realized_gain_in.amount
        )
        db.add(portfolio)
    else:
        # Add to existing realized gain
        portfolio.realized_gain = (
            portfolio.realized_gain or 0.0
        ) + realized_gain_in.amount

    await db.commit()
    await db.refresh(portfolio)

    return portfolio


@router.post("/portfolio/add-realized-loss", response_model=PortfolioSchema)
async def add_realized_loss(
    realized_loss_in: PortfolioRealizedLossAdd,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Add realized loss to current user's portfolio
    """
    # Get user's portfolio
    result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = result.scalars().first()

    # If no portfolio exists, create one
    if not portfolio:
        portfolio = Portfolio(
            user_id=current_user.id, realized_loss=realized_loss_in.amount
        )
        db.add(portfolio)
    else:
        # Add to existing realized loss
        portfolio.realized_loss = (
            portfolio.realized_loss or 0.0
        ) + realized_loss_in.amount

    await db.commit()
    await db.refresh(portfolio)

    return portfolio


@router.post("/portfolio/deduct-fees", response_model=PortfolioSchema)
async def deduct_fees(
    fees_in: PortfolioFeesDeduct,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Deduct fees from current user's portfolio
    """
    # Get user's portfolio
    result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = result.scalars().first()

    # If no portfolio exists, create one
    if not portfolio:
        portfolio = Portfolio(
            user_id=current_user.id, investment_amount=-fees_in.amount
        )
        db.add(portfolio)
    else:
        # Deduct from existing investment amount
        portfolio.investment_amount = (
            portfolio.investment_amount or 0.0
        ) - fees_in.amount

    await db.commit()
    await db.refresh(portfolio)

    return portfolio


@router.get("/portfolio/entries", response_model=List[PortfolioEntryResponse])
async def get_portfolio_entries(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get current user's portfolio entries
    """
    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        return []

    # Get portfolio entries with trading_code relationship
    # The relationship is already configured with lazy="selectin" in the model
    # which should automatically load the relationship
    entries_result = await db.execute(
        select(PortfolioEntry)
        .where(PortfolioEntry.portfolio_id == portfolio.id)
        .order_by(desc(PortfolioEntry.transaction_date))
    )
    entries = entries_result.scalars().all()

    return entries


@router.post("/portfolio/entries", response_model=PortfolioEntryResponse)
async def create_portfolio_entry(
    entry_in: PortfolioEntryCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Create a new portfolio entry (buy or sell)
    """
    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        portfolio = Portfolio(user_id=current_user.id)
        db.add(portfolio)
        await db.commit()
        await db.refresh(portfolio)

    # Verify trading code exists
    trading_code_result = await db.execute(
        select(TradingCode).where(TradingCode.id == entry_in.trading_code_id)
    )
    trading_code = trading_code_result.scalars().first()

    if not trading_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trading code not found",
        )

    # If this is a sell transaction, verify the user has enough shares and calculate realized gain
    if entry_in.transaction_type == "sell":
        # Get all entries for this trading code
        entries_result = await db.execute(
            select(PortfolioEntry)
            .where(
                PortfolioEntry.portfolio_id == portfolio.id,
                PortfolioEntry.trading_code_id == entry_in.trading_code_id,
            )
            .order_by(PortfolioEntry.transaction_date)
        )
        entries = entries_result.scalars().all()

        # Calculate total shares owned and average buy price
        total_shares = 0
        total_buy_investment = 0
        buy_shares = 0

        for entry in entries:
            if entry.transaction_type == "buy":
                total_shares += entry.unit_count
                buy_shares += entry.unit_count
                total_buy_investment += entry.transaction_price * entry.unit_count
            else:  # sell
                total_shares -= entry.unit_count

        # Check if user has enough shares to sell
        if total_shares < entry_in.unit_count:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Not enough shares to sell. You own {total_shares} shares.",
            )

        # Calculate realized gain/loss if there are buy transactions
        if buy_shares > 0:
            average_buy_price = total_buy_investment / buy_shares
            sell_value = entry_in.transaction_price * entry_in.unit_count
            buy_value = average_buy_price * entry_in.unit_count
            realized_gain_loss = sell_value - buy_value

            # Separate gains and losses
            if realized_gain_loss >= 0:
                # It's a gain
                portfolio.realized_gain = (
                    portfolio.realized_gain or 0.0
                ) + realized_gain_loss
            else:
                # It's a loss (convert to positive value for storage)
                portfolio.realized_loss = (portfolio.realized_loss or 0.0) + abs(
                    realized_gain_loss
                )

    # Create new portfolio entry
    entry = PortfolioEntry(
        portfolio_id=portfolio.id,
        trading_code_id=entry_in.trading_code_id,
        transaction_price=entry_in.transaction_price,
        unit_count=entry_in.unit_count,
        transaction_date=entry_in.transaction_date.replace(tzinfo=None),
        transaction_type=entry_in.transaction_type,
    )

    db.add(entry)
    await db.commit()
    await db.refresh(entry)

    return entry


@router.get("/portfolio/dividend-info", response_model=List[DividendInfoResponse])
async def get_dividend_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get dividend information for all trading codes in user's portfolio
    """
    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        return []

    # Get dividend info for this portfolio
    dividend_info_result = await db.execute(
        select(DividendInfo).where(DividendInfo.portfolio_id == portfolio.id)
    )
    dividend_infos = dividend_info_result.scalars().all()

    return dividend_infos


@router.put(
    "/portfolio/dividend-info/{trading_code_id}", response_model=DividendInfoResponse
)
async def update_dividend_info(
    trading_code_id: int,
    dividend_update: DividendInfoUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update dividend information for a specific trading code
    """
    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found",
        )

    # Verify trading code exists
    trading_code_result = await db.execute(
        select(TradingCode).where(TradingCode.id == trading_code_id)
    )
    trading_code = trading_code_result.scalars().first()

    if not trading_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trading code not found",
        )

    # Get or create dividend info
    dividend_info_result = await db.execute(
        select(DividendInfo).where(
            DividendInfo.portfolio_id == portfolio.id,
            DividendInfo.trading_code_id == trading_code_id,
        )
    )
    dividend_info = dividend_info_result.scalars().first()

    if not dividend_info:
        # Create new dividend info
        dividend_info = DividendInfo(
            portfolio_id=portfolio.id, trading_code_id=trading_code_id
        )
        db.add(dividend_info)

    # Update fields
    if dividend_update.last_record_date is not None:
        dividend_info.last_record_date = dividend_update.last_record_date
    if dividend_update.next_record_date is not None:
        dividend_info.next_record_date = dividend_update.next_record_date
    if dividend_update.dividend_per_share is not None:
        dividend_info.dividend_per_share = dividend_update.dividend_per_share

    dividend_info.updated_at = datetime.now()

    await db.commit()
    await db.refresh(dividend_info)

    return dividend_info


@router.get(
    "/portfolio/received-dividends",
    response_model=List[ReceivedDividendWithTradingCode],
)
async def get_received_dividends(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get all received dividends for the user's portfolio with trading code information
    """
    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        return []

    # Get received dividends for this portfolio with trading code information
    received_dividends_result = await db.execute(
        select(ReceivedDividend, TradingCode.code, TradingCode.name)
        .join(TradingCode, ReceivedDividend.trading_code_id == TradingCode.id)
        .where(ReceivedDividend.portfolio_id == portfolio.id)
        .order_by(desc(ReceivedDividend.record_date))
    )

    # Transform the results to include trading code information
    received_dividends = []
    for dividend, trading_code, trading_name in received_dividends_result:
        dividend_dict = {
            "id": dividend.id,
            "portfolio_id": dividend.portfolio_id,
            "trading_code_id": dividend.trading_code_id,
            "record_date": dividend.record_date,
            "share_count": dividend.share_count,
            "dividend_per_share": dividend.dividend_per_share,
            "gross_dividend": dividend.gross_dividend,
            "tax_amount": dividend.tax_amount,
            "net_dividend": dividend.net_dividend,
            "created_at": dividend.created_at,
            "trading_code": trading_code,
            "trading_name": trading_name,
        }
        received_dividends.append(dividend_dict)

    return received_dividends


@router.get(
    "/portfolio/received-dividends/summary", response_model=ReceivedDividendSummary
)
async def get_received_dividends_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get summary of all received dividends for the user's portfolio
    """
    from sqlalchemy import func

    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        return {
            "total_dividend_received": 0,
            "total_tax_deducted": 0,
            "total_net_dividend": 0,
        }

    # Get summary of received dividends
    summary_result = await db.execute(
        select(
            func.sum(ReceivedDividend.gross_dividend).label("total_dividend_received"),
            func.sum(ReceivedDividend.tax_amount).label("total_tax_deducted"),
            func.sum(ReceivedDividend.net_dividend).label("total_net_dividend"),
        ).where(ReceivedDividend.portfolio_id == portfolio.id)
    )

    summary = summary_result.first()

    return {
        "total_dividend_received": summary.total_dividend_received or 0,
        "total_tax_deducted": summary.total_tax_deducted or 0,
        "total_net_dividend": summary.total_net_dividend or 0,
    }


@router.post("/portfolio/dividend-entries", response_model=ReceivedDividendWithTradingCode)
async def add_dividend_entry(
    dividend_entry: DividendEntryCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Add a new dividend entry for the user's portfolio
    """
    from decimal import Decimal

    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found",
        )

    # Verify trading code exists
    trading_code_result = await db.execute(
        select(TradingCode).where(TradingCode.id == dividend_entry.trading_code_id)
    )
    trading_code = trading_code_result.scalars().first()

    if not trading_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trading code not found",
        )

    # Calculate dividend amounts
    gross_dividend = Decimal(str(dividend_entry.share_count)) * dividend_entry.dividend_per_share
    tax_amount = gross_dividend * Decimal("0.10")  # 10% tax
    net_dividend = gross_dividend - tax_amount

    # Create received dividend record
    received_dividend = ReceivedDividend(
        portfolio_id=portfolio.id,
        trading_code_id=dividend_entry.trading_code_id,
        record_date=dividend_entry.record_date,
        share_count=dividend_entry.share_count,
        dividend_per_share=dividend_entry.dividend_per_share,
        gross_dividend=gross_dividend,
        tax_amount=tax_amount,
        net_dividend=net_dividend,
    )

    db.add(received_dividend)

    # Update or create dividend_info record
    today = date.today()
    dividend_info_result = await db.execute(
        select(DividendInfo).where(
            DividendInfo.portfolio_id == portfolio.id,
            DividendInfo.trading_code_id == dividend_entry.trading_code_id,
        )
    )
    dividend_info = dividend_info_result.scalars().first()

    if not dividend_info:
        # Create new dividend info
        dividend_info = DividendInfo(
            portfolio_id=portfolio.id,
            trading_code_id=dividend_entry.trading_code_id
        )
        db.add(dividend_info)

    # Determine if record date is past or future
    if dividend_entry.record_date <= today:
        # Past date - update last_record_date
        dividend_info.last_record_date = dividend_entry.record_date
    else:
        # Future date - update next_record_date
        dividend_info.next_record_date = dividend_entry.record_date

    dividend_info.dividend_per_share = dividend_entry.dividend_per_share
    dividend_info.updated_at = datetime.now()

    await db.commit()
    await db.refresh(received_dividend)

    # Return the dividend with trading code information
    return {
        "id": received_dividend.id,
        "portfolio_id": received_dividend.portfolio_id,
        "trading_code_id": received_dividend.trading_code_id,
        "record_date": received_dividend.record_date,
        "share_count": received_dividend.share_count,
        "dividend_per_share": received_dividend.dividend_per_share,
        "gross_dividend": received_dividend.gross_dividend,
        "tax_amount": received_dividend.tax_amount,
        "net_dividend": received_dividend.net_dividend,
        "dividend_received_date": received_dividend.dividend_received_date,
        "created_at": received_dividend.created_at,
        "trading_code": trading_code.code,
        "trading_name": trading_code.name,
    }


@router.put("/portfolio/received-dividends/{dividend_id}", response_model=ReceivedDividendWithTradingCode)
async def update_received_dividend(
    dividend_id: int,
    dividend_update: ReceivedDividendUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update a received dividend entry
    """
    from decimal import Decimal

    # Get user's portfolio
    portfolio_result = await db.execute(
        select(Portfolio).where(Portfolio.user_id == current_user.id)
    )
    portfolio = portfolio_result.scalars().first()

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found",
        )

    # Get the received dividend record
    dividend_result = await db.execute(
        select(ReceivedDividend, TradingCode.code, TradingCode.name)
        .join(TradingCode, ReceivedDividend.trading_code_id == TradingCode.id)
        .where(
            ReceivedDividend.id == dividend_id,
            ReceivedDividend.portfolio_id == portfolio.id
        )
    )
    result = dividend_result.first()

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dividend record not found",
        )

    received_dividend, trading_code, trading_name = result

    # Update fields if provided
    if dividend_update.dividend_per_share is not None:
        received_dividend.dividend_per_share = dividend_update.dividend_per_share
        # Recalculate amounts
        gross_dividend = Decimal(str(received_dividend.share_count)) * dividend_update.dividend_per_share
        tax_amount = gross_dividend * Decimal("0.10")  # 10% tax
        net_dividend = gross_dividend - tax_amount
        received_dividend.gross_dividend = gross_dividend
        received_dividend.tax_amount = tax_amount
        received_dividend.net_dividend = net_dividend

    if dividend_update.share_count is not None:
        received_dividend.share_count = dividend_update.share_count
        # Recalculate amounts
        gross_dividend = Decimal(str(dividend_update.share_count)) * received_dividend.dividend_per_share
        tax_amount = gross_dividend * Decimal("0.10")  # 10% tax
        net_dividend = gross_dividend - tax_amount
        received_dividend.gross_dividend = gross_dividend
        received_dividend.tax_amount = tax_amount
        received_dividend.net_dividend = net_dividend

    if dividend_update.dividend_received_date is not None:
        received_dividend.dividend_received_date = dividend_update.dividend_received_date

    await db.commit()
    await db.refresh(received_dividend)

    # Return the updated dividend with trading code information
    return {
        "id": received_dividend.id,
        "portfolio_id": received_dividend.portfolio_id,
        "trading_code_id": received_dividend.trading_code_id,
        "record_date": received_dividend.record_date,
        "share_count": received_dividend.share_count,
        "dividend_per_share": received_dividend.dividend_per_share,
        "gross_dividend": received_dividend.gross_dividend,
        "tax_amount": received_dividend.tax_amount,
        "net_dividend": received_dividend.net_dividend,
        "dividend_received_date": received_dividend.dividend_received_date,
        "created_at": received_dividend.created_at,
        "trading_code": trading_code,
        "trading_name": trading_name,
    }
