"use client";

import { PortfolioDetailsContent } from "@/components/portfolio/PortfolioDetailsContent";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { useRouter } from "next/navigation";

export default function PortfolioDetailsModal() {
  const router = useRouter();

  return (
    <Dialog open={true} onOpenChange={() => router.back()}>
      <DialogContent className="!w-full !max-w-[900px] max-h-[95vh]">
        {/* Add the required DialogHeader and a visually hidden DialogTitle */}
        <DialogHeader>
          <VisuallyHidden>
            <DialogTitle>Portfolio Details</DialogTitle>
          </VisuallyHidden>
        </DialogHeader>
        <PortfolioDetailsContent />
      </DialogContent>
    </Dialog>
  );
}
