import logging
from datetime import timedel<PERSON>
from typing import Any

from app import crud
from app.api.deps import get_db
from app.core.config import settings
from app.core.security import create_access_token
from app.schemas.user import Token, User<PERSON>reate, UserLogin, UserResponse
from jose import jwt, JWTError
from app.tasks.dividend_processor import process_expired_dividends
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login", response_model=UserResponse)
async def login_access_token(
    db: AsyncSession = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # For OAuth2 form, username field contains the email
    user = await crud.authenticate(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id), expires_delta=access_token_expires
    )

    # Process expired dividends immediately after login
    try:
        await process_expired_dividends()
        logger.info(f"Dividend processing completed for user {user.id} login")
    except Exception as e:
        logger.error(
            f"Error processing dividends on login for user {user.id}: {str(e)}"
        )
        # Don't fail login if dividend processing fails

    return {
        "id": user.id,
        "email": user.email,
        "full_name": user.full_name,
        "is_admin": user.is_admin,
        "access_token": access_token,
    }


@router.post("/login/email", response_model=UserResponse)
async def login_email(
    user_in: UserLogin,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Login with email and password
    """
    user = await crud.authenticate(db, email=user_in.email, password=user_in.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id), expires_delta=access_token_expires
    )

    # Process expired dividends immediately after login
    try:
        await process_expired_dividends()
        logger.info(f"Dividend processing completed for user {user.id} login")
    except Exception as e:
        logger.error(
            f"Error processing dividends on login for user {user.id}: {str(e)}"
        )
        # Don't fail login if dividend processing fails

    return {
        "id": user.id,
        "email": user.email,
        "full_name": user.full_name,
        "is_admin": user.is_admin,
        "access_token": access_token,
    }


@router.post("/register", response_model=UserResponse)
async def register_user(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Register a new user with email and password
    """
    # Check if user with this email already exists
    existing_user = await crud.get_by_email(db, email=user_in.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered",
        )

    # Create new user
    user = await crud.create(db, obj_in=user_in)

    # Generate access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id), expires_delta=access_token_expires
    )
    return {
        "id": user.id,
        "email": user.email,
        "full_name": user.full_name,
        "is_admin": user.is_admin,
        "access_token": access_token,
    }
