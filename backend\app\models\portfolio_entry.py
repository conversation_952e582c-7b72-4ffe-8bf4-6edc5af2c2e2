from datetime import datetime

from app.db.base_class import Base
from sqlalchemy import Column, DateTime, Float, ForeignKey, Integer, String
from sqlalchemy.orm import relationship


class PortfolioEntry(Base):
    __tablename__ = "portfolio_entries"
    __table_args__ = {"schema": "dse_schema"}

    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(
        Integer, ForeignKey("dse_schema.portfolios.id"), nullable=False
    )
    trading_code_id = Column(
        Integer, ForeignKey("dse_schema.trading_codes.id"), nullable=False
    )
    transaction_price = Column(Float, nullable=False)
    unit_count = Column(Integer, nullable=False)
    transaction_date = Column(DateTime, nullable=False, default=datetime.now)
    transaction_type = Column(String(10), nullable=False, default="buy")

    # Relationships
    portfolio = relationship("Portfolio", back_populates="entries", lazy="selectin")
    trading_code = relationship(
        "TradingCode", back_populates="portfolio_entries", lazy="selectin"
    )
