# Define relationships between models after all models are defined
# This helps avoid circular import issues

from sqlalchemy.orm import relationship
from app.models.user import User
from app.models.portfolio import Portfolio

# Add the portfolios relationship to User
User.portfolios = relationship(
    "Portfolio",
    foreign_keys="Portfolio.user_id",
    cascade="all, delete-orphan",
    lazy="selectin",
)
