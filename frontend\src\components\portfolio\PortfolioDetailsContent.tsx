"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { ArrowUpDown, Search } from "lucide-react";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useMemo, useState } from "react";

interface PortfolioEntry {
  id: number;
  portfolio_id: number;
  trading_code_id: number;
  transaction_price: number;
  unit_count: number;
  transaction_date: string;
  transaction_type: "buy" | "sell";
  trading_code: {
    id: number;
    code: string;
    name: string;
  };
}

type SortField =
  | "transaction_date"
  | "trading_code"
  | "transaction_price"
  | "unit_count"
  | "gain_loss_per_unit"
  | "total_gain_loss";
type SortDirection = "asc" | "desc";

const formatCurrency = (amount: number) => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return "BDT 0.00";
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return "N/A";
  }
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export function PortfolioDetailsContent() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [entries, setEntries] = useState<PortfolioEntry[]>([]);
  const [commission, setCommission] = useState<number>(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortField, setSortField] = useState<SortField>("transaction_date");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");

  // Get live prices from localStorage
  const livePrices = useMemo(() => {
    try {
      const stored = localStorage.getItem("livePrices");
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }, []);

  const fetchPortfolioData = useCallback(async () => {
    if (!session?.user?.accessToken) return;
    setIsLoading(true);
    try {
      const [entriesRes, settingsRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`, {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`, {
          headers: { Authorization: `Bearer ${session.user.accessToken}` },
        }),
      ]);

      if (entriesRes.ok) {
        const entriesData = await entriesRes.json();
        // Filter to show only buy transactions
        const buyEntries = entriesData.filter(
          (entry: PortfolioEntry) => entry.transaction_type === "buy"
        );
        setEntries(buyEntries);
      } else {
        throw new Error("Failed to fetch portfolio entries");
      }

      if (settingsRes.ok) {
        const settings = await settingsRes.json();
        setCommission(settings.commission || 0);
      }
    } catch (error) {
      console.error("Error fetching portfolio data:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while fetching your portfolio details.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [session, toast]);

  useEffect(() => {
    fetchPortfolioData();
  }, [fetchPortfolioData]);

  const handleSort = useCallback((field: SortField) => {
    setSortField((currentField) => {
      if (field === currentField) {
        setSortDirection((currentDirection) =>
          currentDirection === "asc" ? "desc" : "asc"
        );
        return currentField;
      } else {
        setSortDirection("asc");
        return field;
      }
    });
  }, []);

  const processedEntries = useMemo(() => {
    return entries.map((entry) => {
      const currentPrice = livePrices[entry.trading_code.code]?.price || 0;
      const sellPrice = currentPrice * (1 - commission / 100);
      const gainLossPerUnit = sellPrice - entry.transaction_price;
      const totalGainLoss = gainLossPerUnit * entry.unit_count;

      return {
        ...entry,
        currentPrice,
        gainLossPerUnit,
        totalGainLoss,
      };
    });
  }, [entries, livePrices, commission]);

  const filteredAndSortedEntries = useMemo(() => {
    const filtered = processedEntries.filter((entry) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        entry.trading_code.code.toLowerCase().includes(query) ||
        entry.trading_code.name.toLowerCase().includes(query) ||
        entry.transaction_price.toString().includes(query) ||
        entry.unit_count.toString().includes(query) ||
        formatDate(entry.transaction_date).toLowerCase().includes(query)
      );
    });

    return [...filtered].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case "trading_code":
          aValue = a.trading_code.code;
          bValue = b.trading_code.code;
          break;
        case "gain_loss_per_unit":
          aValue = a.gainLossPerUnit;
          bValue = b.gainLossPerUnit;
          break;
        case "total_gain_loss":
          aValue = a.totalGainLoss;
          bValue = b.totalGainLoss;
          break;
        default:
          aValue = a[sortField];
          bValue = b[sortField];
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      const aNum = Number(aValue);
      const bNum = Number(bValue);
      return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
    });
  }, [processedEntries, searchQuery, sortField, sortDirection]);

  // Group entries by date
  const groupedEntries = useMemo(() => {
    const groups: { [key: string]: typeof filteredAndSortedEntries } = {};

    filteredAndSortedEntries.forEach((entry) => {
      const dateKey = new Date(entry.transaction_date).toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(entry);
    });

    // Sort groups by date (most recent first)
    const sortedGroups = Object.keys(groups)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
      .map((dateKey) => ({
        date: dateKey,
        entries: groups[dateKey],
      }));

    return sortedGroups;
  }, [filteredAndSortedEntries]);

  return (
    <Card className="flex flex-col max-h-[calc(95vh-8rem)]">
      <CardHeader className="flex flex-row items-center justify-between flex-shrink-0">
        <div>
          <CardTitle className="text-xl font-bold">Portfolio Details</CardTitle>
          <CardDescription>
            All your buy transactions with current market analysis
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 min-h-0 overflow-hidden">
        <div className="flex items-center mb-4 relative flex-shrink-0">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by trading code, price, or date..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        <div className="flex-1 overflow-y-auto pr-2">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="w-12 h-12 border-4 border-t-blue-500 border-b-blue-500 rounded-full animate-spin mx-auto"></div>
              <p className="mt-4 text-gray-500">Loading portfolio details...</p>
            </div>
          ) : groupedEntries.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p className="text-lg">No buy transactions found.</p>
              <p className="mt-2">
                {searchQuery
                  ? "Try adjusting your search query."
                  : "Your buy transactions will appear here once you add them."}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {groupedEntries.map((group) => (
                <div key={group.date}>
                  <h3 className="text-sm font-semibold mb-3 text-gray-700">
                    {formatDate(group.date)}
                  </h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead
                          onClick={() => handleSort("trading_code")}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center">
                            Trading Code
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                            {sortField === "trading_code" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          onClick={() => handleSort("transaction_price")}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center">
                            Buy Price
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                            {sortField === "transaction_price" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          onClick={() => handleSort("unit_count")}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center">
                            Units
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                            {sortField === "unit_count" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </TableHead>
                        <TableHead>Current Price</TableHead>
                        <TableHead
                          onClick={() => handleSort("gain_loss_per_unit")}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center">
                            Gain/Loss per Unit
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                            {sortField === "gain_loss_per_unit" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </TableHead>
                        <TableHead
                          onClick={() => handleSort("total_gain_loss")}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center">
                            Total Gain/Loss
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                            {sortField === "total_gain_loss" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {group.entries.map((entry, index) => {
                        const isPositivePerUnit = entry.gainLossPerUnit >= 0;
                        const isPositiveTotal = entry.totalGainLoss >= 0;

                        return (
                          <TableRow
                            key={entry.id}
                            className={
                              index % 2 === 0 ? "bg-background" : "bg-muted/50"
                            }
                          >
                            <TableCell className="font-medium">
                              <div>
                                <div className="font-semibold">
                                  {entry.trading_code.code}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {formatCurrency(entry.transaction_price)}
                            </TableCell>
                            <TableCell>{entry.unit_count}</TableCell>
                            <TableCell>
                              {entry.currentPrice > 0
                                ? formatCurrency(entry.currentPrice)
                                : "—"}
                            </TableCell>
                            <TableCell>
                              <span
                                className={
                                  isPositivePerUnit
                                    ? "text-green-600"
                                    : "text-red-600"
                                }
                              >
                                {formatCurrency(
                                  Math.abs(entry.gainLossPerUnit)
                                )}
                                {isPositivePerUnit ? " ↗" : "🔻"}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span
                                className={
                                  isPositiveTotal
                                    ? "text-green-600"
                                    : "text-red-600"
                                }
                              >
                                {formatCurrency(Math.abs(entry.totalGainLoss))}
                                {isPositiveTotal ? " ↗" : "🔻"}
                              </span>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
