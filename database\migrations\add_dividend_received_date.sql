-- Migration to add dividend_received_date column to received_dividends table
-- Run this migration to add the new optional dividend received date field

-- Add the dividend_received_date column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'dse_schema' 
        AND table_name = 'received_dividends' 
        AND column_name = 'dividend_received_date'
    ) THEN
        ALTER TABLE dse_schema.received_dividends 
        ADD COLUMN dividend_received_date DATE NULL;
        
        -- Add index for the new column
        CREATE INDEX IF NOT EXISTS idx_received_dividends_received_date 
        ON dse_schema.received_dividends(dividend_received_date);
        
        -- Add comment to explain the new column
        COMMENT ON COLUMN dse_schema.received_dividends.dividend_received_date 
        IS 'Optional date when dividend was actually received by the user';
    END IF;
END $$;
