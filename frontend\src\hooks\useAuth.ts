import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

/**
 * A simplified hook to access authentication status and session data.
 * It wraps the `useSession` hook from `next-auth/react` and provides
 * a clean and centralized way to manage auth state, relying on
 * NextAuth's built-in session management and caching.
 */
export function useAuth() {
  const { data: session, status } = useSession();

  const router = useRouter();

  useEffect(() => {
    if (
      session?.user &&
      "error" in session.user &&
      session.user.error === "RefreshAccessTokenError"
    ) {
      signOut({ callbackUrl: "/login" });
    }
  }, [session, router]);

  return {
    session,
    status,
    isAuthenticated: status === "authenticated",
    isLoading: status === "loading",
  };
}
