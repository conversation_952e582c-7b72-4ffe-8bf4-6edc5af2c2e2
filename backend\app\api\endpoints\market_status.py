import logging
from datetime import datetime, time
from typing import Any

import aiohttp
import pytz
from fastapi import APIRouter, HTTPException, status

router = APIRouter()

# Logger
logger = logging.getLogger(__name__)

# Add circuit breaker for API failures
api_failure_count = 0
MAX_API_FAILURES = 3


def _get_fallback_market_status() -> dict:
    """
    Determines market status based on Bangladesh trading hours (Sun-Thu, 10 AM - 2:30 PM).
    This is used as a fallback when the primary API fails.
    """
    try:
        # Get the current time in the "Asia/Dhaka" timezone
        dhaka_tz = pytz.timezone("Asia/Dhaka")
        now_dhaka = datetime.now(dhaka_tz)

        # In Python, weekday() is Monday=0, Tuesday=1, ..., Sunday=6
        # DSE is open Sunday to Thursday.
        is_trading_day = now_dhaka.weekday() in [
            6,
            0,
            1,
            2,
            3,
        ]  # Sun, Mon, Tue, Wed, Thu

        # Define market open and close times
        market_open_time = time(10, 0)
        market_close_time = time(14, 30)

        # Check if the current time is within trading hours
        is_trading_hours = market_open_time <= now_dhaka.time() <= market_close_time

        if is_trading_day and is_trading_hours:
            return {
                "MarketStatus": "Open",
                "is_open": True,
                "source": "fallback_by_time",
            }
        else:
            return {
                "MarketStatus": "Closed",
                "is_open": False,
                "source": "fallback_by_time",
            }

    except Exception as e:
        logger.error(f"Error in fallback logic: {str(e)}")
        return {
            "MarketStatus": "Closed",
            "is_open": False,
            "source": "fallback_error",
            "error": "Fallback logic failed",
        }


async def fetch_market_status_from_source() -> dict:
    """
    Fetch market status from amarstock.com API with proper error handling
    """
    global api_failure_count

    url = "https://www.amarstock.com/Info/DSE"

    try:
        timeout = aiohttp.ClientTimeout(total=10)  # 10 second timeout
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    market_status = data.get("MarketStatus", "Closed")
                    api_failure_count = 0  # Reset failure count on success
                    logger.info(f"Market status retrieved: {market_status}")
                    return {
                        "MarketStatus": market_status,
                        "is_open": market_status != "Closed",
                        "source": "amarstock.com",
                    }
                else:
                    logger.warning(f"API returned status code: {response.status}")
                    api_failure_count += 1

    except Exception as e:
        logger.error(f"Error fetching market status: {str(e)}")
        api_failure_count += 1

    # If we have too many failures, return closed status
    if api_failure_count >= MAX_API_FAILURES:
        logger.warning(
            f"API failure limit reached ({api_failure_count}). Using time-based fallback."
        )
        return _get_fallback_market_status()

    # If failures are below the limit, return a temporary failure status
    logger.warning(
        f"API has failed {api_failure_count} time(s). Reporting market as closed."
    )

    return {
        "MarketStatus": "Closed",
        "is_open": False,
        "source": "fallback",
        "error": "Failed to fetch from source",
    }


@router.get("/status")
async def get_market_status() -> Any:
    """
    Get current market status from amarstock.com
    This endpoint serves as a proxy to avoid CORS issues in frontend
    """
    try:
        result = await fetch_market_status_from_source()
        return result
    except Exception as e:
        logger.error(f"Unexpected error in market status endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch market status",
        )


@router.get("/is-open")
async def is_market_open() -> Any:
    """
    Simple endpoint that returns just the boolean market open status
    """
    try:
        result = await fetch_market_status_from_source()
        return {"is_open": result["is_open"]}
    except Exception as e:
        logger.error(f"Unexpected error in market open check: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check if market is open",
        )
