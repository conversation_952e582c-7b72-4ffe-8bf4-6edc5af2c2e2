import { isMarketOpen } from "@/utils/utils";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useToast } from "./use-toast";

export interface LivePrice {
  trading_code: string;
  price: number;
  last_updated: string;
}

export interface LivePricesApiResponse {
  prices: Record<string, LivePrice>;
}

const LIVE_PRICES_CACHE_KEY = "livePrices";
const LIVE_PRICES_TIMESTAMP_KEY = "livePricesTimestamp";
const LIVE_PRICES_CACHE_DURATION_MS = 5 * 60 * 1000;
const POLLING_INTERVAL_MS = 300000; // 5 minutes, adjust as needed
const FETCH_DEBOUNCE_MS = 10000;

export function useLivePrices() {
  const { data: session } = useSession();
  const { toast } = useToast();

  const [livePrices, setLivePrices] = useState<Record<string, LivePrice>>(
    () => {
      if (typeof window !== "undefined") {
        try {
          const cachedData = localStorage.getItem(LIVE_PRICES_CACHE_KEY);
          const cachedTimestamp = localStorage.getItem(
            LIVE_PRICES_TIMESTAMP_KEY
          );

          if (cachedData && cachedTimestamp) {
            const timestamp = parseInt(cachedTimestamp, 10);
            if (Date.now() - timestamp < LIVE_PRICES_CACHE_DURATION_MS) {
              return JSON.parse(cachedData);
            }
          }
        } catch (error) {
          console.error("Error loading cached live prices:", error);
          localStorage.removeItem(LIVE_PRICES_CACHE_KEY);
          localStorage.removeItem(LIVE_PRICES_TIMESTAMP_KEY);
        }
      }
      return {};
    }
  );

  const livePricesRef = useRef(livePrices);
  useEffect(() => {
    livePricesRef.current = livePrices;
  }, [livePrices]);

  const [isLoading, setIsLoading] = useState(false);
  const [isHookActive, setIsHookActive] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastFetchTimeRef = useRef<number>(0);
  const initialFetchDoneForCurrentSessionRef = useRef<boolean>(false);

  const lastSetupRef = useRef<{ active: boolean; codes: string[] }>({
    active: false,
    codes: [],
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        if (Object.keys(livePrices).length > 0) {
          localStorage.setItem(
            LIVE_PRICES_CACHE_KEY,
            JSON.stringify(livePrices)
          );
          localStorage.setItem(
            LIVE_PRICES_TIMESTAMP_KEY,
            Date.now().toString()
          );
        } else {
          localStorage.removeItem(LIVE_PRICES_CACHE_KEY);
          localStorage.removeItem(LIVE_PRICES_TIMESTAMP_KEY);
        }
      } catch (error) {
        console.error(
          "Error saving/clearing live prices in localStorage:",
          error
        );
      }
    }
  }, [livePrices]);

  const fetchLivePrices = useCallback(
    async (tradingCodes: string[], forceRefresh = false) => {
      if (!session?.user?.accessToken || !tradingCodes.length) {
        if (!tradingCodes.length) setIsLoading(false);
        return;
      }

      const now = Date.now();
      if (!forceRefresh && now - lastFetchTimeRef.current < FETCH_DEBOUNCE_MS) {
        return;
      }

      const noPricesCurrentlyLoaded =
        Object.keys(livePricesRef.current).length === 0;
      const marketIsOpen = await isMarketOpen();

      if (!marketIsOpen && !noPricesCurrentlyLoaded && !forceRefresh) {
        return;
      }

      setIsLoading(true);
      lastFetchTimeRef.current = now;

      try {
        const response = await fetch(
          `${
            process.env.NEXT_PUBLIC_API_URL
          }/live-prices?codes=${tradingCodes.join(",")}`,
          { headers: { Authorization: `Bearer ${session.user.accessToken}` } }
        );

        if (response.ok) {
          const data: LivePricesApiResponse = await response.json();
          setLivePrices(data.prices || {});
        } else {
          const errorText = await response.text();
          console.error(
            "Failed to fetch live prices:",
            response.status,
            errorText
          );
        }
      } catch (error) {
        console.error("Error fetching live prices:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [session]
  );

  const startPolling = useCallback(
    (currentTradingCodes: string[]) => {
      if (!currentTradingCodes.length) return;

      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }

      fetchLivePrices(currentTradingCodes, true);
      initialFetchDoneForCurrentSessionRef.current = true;

      pollingIntervalRef.current = setInterval(async () => {
        const marketShouldBeOpen = await isMarketOpen();
        if (marketShouldBeOpen) {
          fetchLivePrices(currentTradingCodes, false);
        }
      }, POLLING_INTERVAL_MS);
    },
    [fetchLivePrices]
  );

  const setDashboardActive = useCallback(
    (shouldBeActive: boolean, newTradingCodes: string[] = []) => {
      const sortedNewCodes = [...newTradingCodes].sort();
      const sortedCurrentCodes = [...lastSetupRef.current.codes].sort();

      if (
        shouldBeActive === lastSetupRef.current.active &&
        JSON.stringify(sortedNewCodes) === JSON.stringify(sortedCurrentCodes)
      ) {
        return;
      }

      lastSetupRef.current = {
        active: shouldBeActive,
        codes: [...newTradingCodes],
      };

      if (shouldBeActive !== isHookActive) {
        setIsHookActive(shouldBeActive);
      }

      if (shouldBeActive && newTradingCodes.length > 0) {
        startPolling(newTradingCodes);
      } else {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        initialFetchDoneForCurrentSessionRef.current = false;
      }
    },
    [startPolling, isHookActive]
  );

  const refreshPrices = useCallback(
    async (currentTradingCodes: string[]) => {
      if (!currentTradingCodes.length) {
        return;
      }
      await fetchLivePrices(currentTradingCodes, true);
      toast({
        title: "Prices Refreshed",
        description: "Live prices have been updated.",
      });
    },
    [fetchLivePrices, toast]
  );

  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  return {
    livePrices,
    isLoading,
    isMarketOpen,
    setDashboardActive,
    refreshPrices,
  };
}
