from datetime import datetime  # Change from date to datetime
from typing import List, Optional

# Import directly instead of using TYPE_CHECKING
from app.schemas.trading import TradingCodeResponse
from pydantic import BaseModel
from typing_extensions import (
    Literal,
)  # Use typing_extensions for Python 3.7 compatibility


class PortfolioEntryBase(BaseModel):
    trading_code_id: int
    transaction_price: float
    unit_count: int
    transaction_date: datetime  # Changed from date to datetime
    transaction_type: Literal["buy", "sell"] = "buy"


class PortfolioEntryCreate(PortfolioEntryBase):
    pass


class PortfolioEntryUpdate(BaseModel):
    trading_code_id: Optional[int] = None
    transaction_price: Optional[float] = None
    unit_count: Optional[int] = None
    transaction_date: Optional[datetime] = None  # Changed from date to datetime
    transaction_type: Optional[Literal["buy", "sell"]] = None


class PortfolioEntryInDBBase(PortfolioEntryBase):
    id: int
    portfolio_id: int

    class Config:
        from_attributes = True


class PortfolioEntry(PortfolioEntryInDBBase):
    pass


class PortfolioEntryResponse(PortfolioEntryInDBBase):
    # Include the trading_code relationship in the response
    trading_code: Optional["TradingCodeResponse"] = None

    class Config:
        from_attributes = True


class PortfolioEntryWithDetails(PortfolioEntry):
    trading_code: str
    trading_name: str


class PortfolioBase(BaseModel):
    commission: float = 0.0
    investment_amount: float = 0.0
    realized_gain: float = 0.0
    realized_loss: float = 0.0


class PortfolioCreate(PortfolioBase):
    pass


class PortfolioUpdate(BaseModel):
    commission: Optional[float] = None
    investment_amount: Optional[float] = None
    realized_gain: Optional[float] = None
    realized_loss: Optional[float] = None


class PortfolioInvestmentAdd(BaseModel):
    amount: float


class PortfolioRealizedGainAdd(BaseModel):
    amount: float


class PortfolioRealizedLossAdd(BaseModel):
    amount: float


class PortfolioFeesDeduct(BaseModel):
    amount: float


class PortfolioInDBBase(PortfolioBase):
    id: int
    user_id: int

    class Config:
        from_attributes = True


class Portfolio(PortfolioInDBBase):
    pass


class PortfolioWithEntries(Portfolio):
    entries: List[PortfolioEntryWithDetails] = []
